"use strict";

const BaseJoi = require("@hapi/joi");
const Extension = require("@hapi/joi-date");
const Joi = BaseJoi.extend(Extension);
const SchemaJoi = new Object();

//TaskList Query Parameters Schema
SchemaJoi.TaskListQuery = Joi.object().keys({
  TaskListType: Joi.string().min(1).max(1).required(),
  TaskListGroupKey: Joi.string().alphanum().min(1).max(8).required(),
  GroupCounter: Joi.string().alphanum().min(1).max(2).required(),
});

//Service Order Details Query Parameters Schema
SchemaJoi.OrderQuery = Joi.object().keys({
  OrderID: Joi.string().min(1).max(12).required(),
  Ltext: Joi.string().min(1).max(1).required(),
});

//Standard Text Key & Characteristic Schema
SchemaJoi.StkCharQuery = Joi.object().keys({
  StanderdTextKey: Joi.string().min(1).max(7),
  Characteristic: Joi.string().min(1).max(30),
});

//Service Order Maintenance Parameters Schema
SchemaJoi.OrderMaintain = Joi.object().keys({
  HEADER: Joi.object().keys({
    IT_HEADER: Joi.object().keys({
      ORDERID: Joi.string().max(12).required(),
      TECHNICALCOMPLETE: Joi.boolean(),
      ORDER_TYPE: Joi.string().max(4),
      PLANPLANT: Joi.string().max(4),
      SCENARIO: Joi.string().max(4),
      BUS_AREA: Joi.string().max(4),
      MN_WK_CTR: Joi.string().max(8),
      PLANT: Joi.string().max(4),
      PMACTTYPE: Joi.string().max(3),
      PLANGROUP: Joi.string().max(3),
      SYSTCOND: Joi.string().max(1),
      FUNCT_LOC: Joi.string().max(30),
      EQUIPMENT: Joi.string().max(18),
      SERIALNO: Joi.string().max(18),
      MATERIAL: Joi.string().max(18),
      ASSEMBLY: Joi.string().max(18),
      DEVICEDATA: Joi.string().max(40),
      MAINTPLANT: Joi.string().max(4),
      LOCATION: Joi.string().max(10),
      MAINTROOM: Joi.string().max(8),
      PLSECTN: Joi.string().max(3),
      LOC_WK_CTR: Joi.string().max(8),
      ABCINDIC: Joi.string().max(1),
      SORTFIELD: Joi.string().max(30),
      PROFIT_CTR: Joi.string().max(10),
      RESPCCTR: Joi.string().max(10),
      FUNC_AREA: Joi.string().max(16),
      SUPERIOR_NETWORK: Joi.string().max(12),
      SUPERIOR_ACTIVITY: Joi.string().max(4),
      WBS_ELEM: Joi.string().max(8),
      PROCESSING_GROUP: Joi.string().max(2),
      TAXJURCODE: Joi.string().max(15),
      LOC_COMP_CODE: Joi.string().max(4),
      ASSET_NO: Joi.string().max(12),
      SUB_NUMBER: Joi.string().max(4),
      LOC_BUS_AREA: Joi.string().max(4),
      COSTCENTER: Joi.string().max(10),
      LOC_WBS_ELEM: Joi.string().max(8),
      STANDORDER: Joi.string().max(12),
      SETTLORDER: Joi.string().max(12),
      SALESORG: Joi.string().max(4),
      DISTR_CHAN: Joi.string().max(2),
      DIVISION: Joi.string().max(2),
      ORDPLANID: Joi.string().max(1),
      START_DATE: Joi.date().format("YYYYMMDD"),
      FINISH_DATE: Joi.date().format("YYYYMMDD"),
      BASICSTART: Joi.date().format("HHmmss"),
      BASIC_FIN: Joi.date().format("HHmmss"),
      PRIORITY: Joi.string().max(1),
      REVISION: Joi.string().max(8),
      VERSION: Joi.string().max(2),
      SCHED_TYPE: Joi.string().max(1),
      AUTOSCHED: Joi.string().max(1),
      CAP_REQMTS: Joi.string().max(1),
      SCHEDULING_EXACT_BREAK_TIMES: Joi.string().max(1),
      MRP_RELEVANT: Joi.string().max(1),
      SALES_ORD: Joi.string().max(10),
      S_ORD_ITEM: Joi.string().max(6),
      CALC_MOTIVE: Joi.string().max(2),
      INVEST_PROFILE: Joi.string().max(6),
      SCALE: Joi.string().max(2),
      INV_REASON: Joi.string().max(2),
      ENVIR_INVEST: Joi.string().max(5),
      ESTIMATED_COSTS: Joi.string().max(11),
      CURRENCY: Joi.string().max(5),
      CURRENCY_ISO: Joi.string().max(3),
      CSTG_SHEET: Joi.string().max(6),
      OVERHEAD_KEY: Joi.string().max(6),
      RES_ANAL_KEY: Joi.string().max(6),
      SHORT_TEXT: Joi.string().max(40),
      ASSEMBLY_EXTERNAL: Joi.string().max(40),
      ASSEMBLY_GUID: Joi.string().max(32),
      ASSEMBLY_VERSION: Joi.string().max(10),
      MATERIAL_EXTERNAL: Joi.string().max(40),
      MATERIAL_GUID: Joi.string().max(32),
      MATERIAL_VERSION: Joi.string().max(10),
      NOTIF_NO: Joi.string().max(12),
      TECO_REF_DATE: Joi.date().format("YYYYMMDD"),
      TECO_REF_TIME: Joi.date().format("HHmmss"),
      CALID: Joi.string().max(2),
      KALSN: Joi.string().max(2),
      SUPERIOR_ORDERID: Joi.string().max(12),
      NOTIF_TYPE: Joi.string().max(2),
      START_POINT: Joi.string().max(18),
      END_POINT: Joi.string().max(18),
      LINEAR_LENGTH: Joi.string().max(18),
      LINEAR_UNIT: Joi.string().max(3),
      LINEAR_UNIT_ISO: Joi.string().max(3),
      FIRST_OFFSET_TYPE_CODE: Joi.string().max(2),
      FIRST_OFFSET_VALUE: Joi.string().max(18),
      FIRST_OFFSET_UNIT: Joi.string().max(3),
      FIRST_OFFSET_UNIT_ISO: Joi.string().max(3),
      SECOND_OFFSET_TYPE_CODE: Joi.string().max(2),
      SECOND_OFFSET_VALUE: Joi.string().max(18),
      SECOND_OFFSET_UNIT: Joi.string().max(3),
      SECOND_OFFSET_UNIT_ISO: Joi.string().max(3),
      MARKER_START_POINT: Joi.string().max(18),
      MARKER_DISTANCE_START_POINT: Joi.string().max(18),
      MARKER_END_POINT: Joi.string().max(18),
      MARKER_DISTANCE_END_POINT: Joi.string().max(18),
      MARKER_DISTANCE_UNIT: Joi.string().max(3),
      MARKER_DISTANCE_UNIT_ISO: Joi.string().max(3),
      REFERENCE_ORDER: Joi.string().max(12),
      USERSTATUS: Joi.string().max(100),
    }),
    IT_HEADER_SRV: Joi.object().keys({
      OBJECT_NO: Joi.string().max(22),
      SALESORG: Joi.string().max(4),
      DISTR_CHAN: Joi.string().max(2),
      DIVISION: Joi.string().max(2),
      SALES_GRP: Joi.string().max(3),
      SALES_OFF: Joi.string().max(4),
      PURCH_NO_C: Joi.string().max(35),
      PURCH_DATE: Joi.date().format("YYYYMMDD"),
      MATERIAL: Joi.string().max(18),
      QUANTITY: Joi.string().max(13),
      BASE_UOM: Joi.string().max(3),
      BASE_UOM_ISO: Joi.string().max(3),
      CONFIGURATION: Joi.string().max(18),
      DLI_PROFILE: Joi.string().max(8),
      BILLING_FORM: Joi.string().max(2),
      MATERIAL_EXTERNAL: Joi.string().max(40),
      MATERIAL_GUID: Joi.string().max(32),
      MATERIAL_VERSION: Joi.string().max(10),
    }),
  }),
  PARTNERS: Joi.array().items(
    Joi.object().keys({
      ORDERID: Joi.string().max(12),
      PARTN_ROLE: Joi.string().max(2),
      PARTN_ROLE_OLD: Joi.string().max(2),
      PARTNER: Joi.string().max(12),
      PARTNER_OLD: Joi.string().max(12),
    })
  ),
  OPERATIONS: Joi.array().items(
    Joi.object().keys({
      IT_OPERATION: Joi.object().keys({
        ACTIVITY: Joi.string().max(4),
        SUB_ACTIVITY: Joi.string().max(4),
        CONTROL_KEY: Joi.string().max(4).when("ACTIVITY", {
          is: Joi.exist(),
          then: Joi.optional(),
          otherwise: Joi.required(),
        }),
        WORK_CNTR: Joi.string().max(8).when("ACTIVITY", {
          is: Joi.exist(),
          then: Joi.optional(),
          otherwise: Joi.required(),
        }),
        PLANT: Joi.string().max(4).when("ACTIVITY", {
          is: Joi.exist(),
          then: Joi.optional(),
          otherwise: Joi.required(),
        }),
        STANDARD_TEXT_KEY: Joi.string().max(7).when("ACTIVITY", {
          is: Joi.exist(),
          then: Joi.optional(),
          otherwise: Joi.required(),
        }),
        DESCRIPTION: Joi.string(), //.max(40),
        LANGU: Joi.string().max(1),
        LANGU_ISO: Joi.string().max(2),
        NO_OF_TIME_TICKETS: Joi.string().max(3),
        WAGETYPE: Joi.string().max(4),
        SUITABILITY: Joi.string().max(2),
        WAGEGROUP: Joi.string().max(3),
        SORT_FLD: Joi.string().max(10),
        VENDOR_NO: Joi.string().max(10),
        QUANTITY: Joi.string().max(13),
        BASE_UOM: Joi.string().max(3),
        BASE_UOM_ISO: Joi.string().max(3),
        PRICE: Joi.string().max(11),
        PRICE_UNIT: Joi.string().max(5),
        COST_ELEMENT: Joi.string().max(10),
        CURRENCY: Joi.string().max(5),
        CURRENCY_ISO: Joi.string().max(3),
        INFO_REC: Joi.string().max(10),
        PURCH_ORG: Joi.string().max(4),
        PUR_GROUP: Joi.string().max(3),
        MATL_GROUP: Joi.string().max(9),
        AGREEMENT: Joi.string().max(10),
        AGMT_ITEM: Joi.string().max(5),
        PREQ_NAME: Joi.string().max(12),
        TRACKINGNO: Joi.string().max(10),
        NUMBER_OF_CAPACITIES: Joi.string().max(3),
        PERCENT_OF_WORK: Joi.string().max(3),
        CALC_KEY: Joi.string().max(1),
        ACTTYPE: Joi.string().max(6).when("ACTIVITY", {
          is: Joi.exist(),
          then: Joi.optional(),
          otherwise: Joi.required(),
        }),
        SYSTCOND: Joi.string().max(1),
        ASSEMBLY: Joi.string().max(18),
        INT_DISTR: Joi.string().max(8),
        GR_RCPT: Joi.string().max(12),
        UNLOAD_PT: Joi.string().max(25),
        PERS_NO: Joi.string().max(8),
        FW_ORDER: Joi.string().max(10),
        ORDER_ITEM: Joi.string().max(5),
        PLND_DELRY: Joi.string().max(3),
        DURATION_NORMAL: Joi.string().max(5),
        DURATION_NORMAL_UNIT: Joi.string().max(3),
        DURATION_NORMAL_UNIT_ISO: Joi.string().max(3),
        CONSTRAINT_TYPE_START: Joi.string().max(1),
        CONSTRAINT_TYPE_FINISH: Joi.string().max(1),
        WORK_ACTIVITY: Joi.string().max(7),
        UN_WORK: Joi.string().max(3),
        UN_WORK_ISO: Joi.string().max(3),
        START_CONS: Joi.date().format("YYYYMMDD"),
        STRTTIMCON: Joi.date().format("HHmmss"),
        FIN_CONSTR: Joi.date().format("YYYYMMDD"),
        FINTIMCONS: Joi.date().format("HHmmss"),
        EXECFACTOR: Joi.string().max(3),
        MRP_RELEVANT: Joi.string().max(1),
        FIELD_KEY: Joi.string().max(7),
        USR00: Joi.string().max(20),
        USR01: Joi.string().max(20),
        USR02: Joi.string().max(10),
        USR03: Joi.string().max(10),
        USR04: Joi.string().max(13).when("ACTIVITY", {
          is: Joi.exist(),
          then: Joi.optional(),
          otherwise: Joi.required(),
        }),
        USE04: Joi.string().max(3),
        USE04_ISO: Joi.string().max(3),
        USR05: Joi.string().max(13),
        USE05: Joi.string().max(3),
        USE05_ISO: Joi.string().max(3),
        USR06: Joi.string().max(13),
        USE06: Joi.string().max(5),
        USE06_ISO: Joi.string().max(3),
        USR07: Joi.string().max(13),
        USE07: Joi.string().max(5),
        USE07_ISO: Joi.string().max(3),
        USR08: Joi.date().format("YYYYMMDD"),
        USR09: Joi.date().format("YYYYMMDD"),
        USR10: Joi.string().max(1),
        USR11: Joi.string().max(1),
        ASSEMBLY_EXTERNAL: Joi.string().max(40),
        ASSEMBLY_GUID: Joi.string().max(32),
        ASSEMBLY_VERSION: Joi.string().max(10),
        EQUIPMENT: Joi.string().max(18),
        FUNCT_LOC: Joi.string().max(40),
        SUBCONTRACTING: Joi.string().max(1),
        START_POINT: Joi.string().max(18),
        END_POINT: Joi.string().max(18),
        LINEAR_LENGTH: Joi.string().max(18),
        LINEAR_UNIT: Joi.string().max(3),
        LINEAR_UNIT_ISO: Joi.string().max(3),
        FIRST_OFFSET_TYPE_CODE: Joi.string().max(2),
        FIRST_OFFSET_VALUE: Joi.string().max(18),
        FIRST_OFFSET_UNIT: Joi.string().max(3),
        FIRST_OFFSET_UNIT_ISO: Joi.string().max(3),
        SECOND_OFFSET_TYPE_CODE: Joi.string().max(2),
        SECOND_OFFSET_VALUE: Joi.string().max(18),
        SECOND_OFFSET_UNIT: Joi.string().max(3),
        SECOND_OFFSET_UNIT_ISO: Joi.string().max(3),
        MARKER_START_POINT: Joi.string().max(18),
        MARKER_DISTANCE_START_POINT: Joi.string().max(18),
        MARKER_END_POINT: Joi.string().max(18),
        MARKER_DISTANCE_END_POINT: Joi.string().max(18),
        MARKER_DISTANCE_UNIT: Joi.string().max(3),
        MARKER_DISTANCE_UNIT_ISO: Joi.string().max(3),
        MAINTENANCE_ACTIVITY_TYPE: Joi.string().max(3),
        BUS_AREA: Joi.string().max(4),
        WBS_ELEM: Joi.string().max(8),
        PROFIT_CTR: Joi.string().max(10),
        CSTG_SHEET: Joi.string().max(6),
        OVERHEAD_KEY: Joi.string().max(6),
        TAXJURCODE: Joi.string().max(15),
        OBJECTCLASS: Joi.string().max(2),
        FUNC_AREA: Joi.string().max(16),
        USERSTATUS: Joi.string().max(100),
        ACTUAL_START_DATE: Joi.date().format("YYYYMMDD"),
        ACTUAL_START_TIME: Joi.date().format("HHmmss"),
        ACTUAL_FINISH_DATE: Joi.date().format("YYYYMMDD"),
        ACTUAL_FINISH_TIME: Joi.date().format("HHmmss"),
      }),
      CHARACTERISTICS: Joi.object().keys({}).pattern(/./, Joi.string()),
    })
  ),
  IT_TASKLISTS: Joi.array().items(
    Joi.object().keys({
      TASK_LIST_TYPE: Joi.string().max(1),
      TASK_LIST_GROUP: Joi.string().max(8),
      GROUP_COUNTER: Joi.string().max(2),
      DELETE_OLD_OPERATIONS: Joi.string().max(1),
      WORKCENTER_SEL: Joi.string().max(8),
      PLANT_SEL: Joi.string().max(4),
      WORKCENTER_USE: Joi.string().max(8),
      PLANT_USE: Joi.string().max(4),
      USE_WORKCENTER_FROM_HEAD: Joi.string().max(1),
    })
  ),
});

//Service Order Details Query Parameters Schema
SchemaJoi.CostMaintenanceOrderQuery = Joi.object().keys({
  SalesOrderID: Joi.string().min(10).max(10).empty([null, ""]).allow(null, ""),
  ServiceOrderID: Joi.string().min(12).max(12).empty([null, ""]).allow(null, "")
}).or('SalesOrderID', 'ServiceOrderID').required();

module.exports = SchemaJoi;