"use strict";

require("dotenv").config();
const router = require("express").Router();
const sapClient = require("../modules/SapModules");
const authentication = require("../modules/Authentication");

// Authentication check
router.use(authentication);

// Standard-Text-Key & Characteristic mapping update
router.get("/CharacteristicAassignment", (req, res) => {
  const sap = new sapClient(req);
  sap
    .open()
    .then(() => sap.get_ZLP_SORD_LAY_AT())
    .then((StkChar) => sap.clear_cache(StkChar))
    .then((StkChar) => sap.Close(StkChar))
    .then((retn) => res.status(200).send(retn))
    .catch((err) => res.status(400).send(err));
});

// Standard-Text-Key & Characteristic mapping update
router.get("/CharacteristicMapping", (req, res) => {
  const sap = new sapClient(req);
  sap
    .open()
    .then(() => sap.get_STK_Char_Validation(req.query))
    .then((query) => sap.get_STK_Char_Mapping(query))
    .then((StkChar) => sap.Close(StkChar))
    .then((retn) => res.status(200).send(retn))
    .catch((err) => res.status(400).send(err));
});

// Tasklist Query
router.get("/TaskList", (req, res) => {
  const sap = new sapClient(req);
  sap
    .open()
    .then(() => sap.alm_tasklist_validation(req.query))
    .then((query) => sap.GetTaskList(query))
    .then((tasklist) => sap.Close(tasklist))
    .then((retn) => res.status(200).send(retn))
    .catch((err) => res.status(400).send(err));
});

// Sales & Service Order Details with Characteristics & LongText
router.get("/details", (req, res) => {
  const sap = new sapClient(req);
  sap
    .open()
    .then(() => sap.alm_orderid_validation(req.query))
    .then((payload) => sap.get_Alm_order_details(payload))
    .then((almdata) => sap.Close(almdata))
    .then((retn) => res.status(200).send(retn))
    .catch((err) => res.status(400).send(err));
});

// Maintain - Sales & Service order Payload
router.get("/maintain", (req, res) => {
  const sap = new sapClient(req);
  sap
    .open()
    .then(() => sap.get_maintain_payload())
    .then((almdata) => sap.Close(almdata))
    .then((retn) => res.status(200).send(retn))
    .catch((err) => res.status(400).send(err));
});

// Maintain Service Order
router.post("/maintain", (req, res) => {
  const sap = new sapClient(req);
  sap
    .open()
    .then(() => sap.alm_payload_validation(req.body))
    .then((payload) => sap.get_ref_almdata(payload))
    .then((almdata) => sap.Prepare_Bapi_Export(almdata))
    .then((almdata) => sap.Prepare_Bapi_Post(almdata))
    .then((almdata) => sap.Close(almdata))
    .then((retn) => res.status(200).send(retn))
    .catch((err) => res.status(400).send(err));
});

// Get Service Order Actual Cost
router.get("/Costs_Actual_Periods", (req, res) => {
  const sap = new sapClient(req);
  sap
    .get_alm_order_validation(req.query)
    .then((orderNumber) => sap.get_cost_maintenance_order(orderNumber))
    .then((retn) => res.status(200).send(retn))
    .catch((err) => res.status(400).send(err));
});

module.exports = router;