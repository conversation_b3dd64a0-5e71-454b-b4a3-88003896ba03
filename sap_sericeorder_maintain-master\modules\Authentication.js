"use strict";

require("dotenv").config();
const jwt = require("express-jwt");
const JwksRsa = require("jwks-rsa");
const auth = require("basic-auth");

module.exports = async (req, res, next) => {
  try {
    req.session.credentials = await Authentication(req, res);
    next();
  } catch (error) {
    res.status(400).send(error);
  }
};

// Validate Basic / Bearer / Cookie - Authentication
const Authentication = (req, res) => {
  return new Promise(async (resolve, reject) => {
    // Basic Authentication
    if (req.headers.authorization && req.headers.authorization.split(" ")[0] === "Basic") {
      const credentials = auth(req);
      if (credentials.name && credentials.pass)
        resolve({
          user: credentials.name,
          passwd: credentials.pass,
          ashost: process.env.ASHOST,
          sysnr: process.env.SYSNR,
          client: process.env.CLIENT,
          lang: process.env.LANGU,
        });

      // <PERSON><PERSON> or <PERSON><PERSON> validation
    } else if (
      (req.headers.authorization && req.headers.authorization.split(" ")[0] === "Bearer") ||
      req.cookies["Token"]
    ) {
      try {
        const jwt = await JwtTokenValidation(req, res);
        if (jwt)
          resolve({
            user: process.env.SAPUSER,
            passwd: process.env.SAPPSW,
            ashost: process.env.ASHOST,
            sysnr: process.env.SYSNR,
            client: process.env.CLIENT,
            lang: process.env.LANGU,
          });
      } catch (error) {
        reject(error);
      }
    } else {
      reject({ message: "Access denied: Authentication failed!" });
    }
  });
};

// Validate JWT
const JwtTokenValidation = (req, res) => {
  return new Promise((resolve, reject) => {
    jwt({
      secret: JwksRsa.expressJwtSecret({
        cache: true,
        rateLimit: true,
        jwksRequestsPerMinute: 5,
        jwksUri: process.env.JWTLINK,
      }),
      getToken: (req) => {
        if (req.headers.authorization && req.headers.authorization.split(" ")[0] === "Bearer") {
          return req.headers.authorization.split(" ")[1];
        } else if (req.cookies["Token"]) {
          return req.cookies["Token"];
        } else {
          return null;
        }
      },
      requestProperty: "jwt",
      algorithms: ["RS256"],
    })(req, res, (err) => {
      if (err) reject({ message: "Access denied: Invalid Token!" });
      resolve(true);
    });
  });
};
