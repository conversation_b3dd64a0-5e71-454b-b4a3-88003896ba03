{"HEADER": {"IT_HEADER": {"ORDERID": "Order Number", "TECHNICALCOMPLETE": "Set X for Technically Complete", "ORDER_TYPE": "Order Type", "PLANPLANT": "Maintenance Planning Plant", "SCENARIO": "Scenario or Subscreen Category", "BUS_AREA": "Business Area", "MN_WK_CTR": "Main work center for maintenance tasks", "PLANT": "Plant associated with main work center", "PMACTTYPE": "Maintenance activity type", "PLANGROUP": "Planner Group for Customer Service and Plant Maintenance", "SYSTCOND": "Syst.Condition", "FUNCT_LOC": "Functional Location", "EQUIPMENT": "Equipment Number", "SERIALNO": "Serial Number", "MATERIAL": "Material Number", "ASSEMBLY": "Assembly", "DEVICEDATA": "Additional Device Data", "MAINTPLANT": "Maintenance plant", "LOCATION": "Asset location", "MAINTROOM": "Room", "PLSECTN": "Plant section", "LOC_WK_CTR": "Work center", "ABCINDIC": "ABC indicator for technical object", "SORTFIELD": "Sort field", "PROFIT_CTR": "Profit Center", "RESPCCTR": "Responsible cost center", "FUNC_AREA": "Functional Area", "SUPERIOR_NETWORK": "Number of superior network", "SUPERIOR_ACTIVITY": "Activity number in network and standard network", "WBS_ELEM": "Work Breakdown Structure Element (WBS Element)", "PROCESSING_GROUP": "Processing group", "TAXJURCODE": "Tax Jurisdiction", "LOC_COMP_CODE": "Company Code", "ASSET_NO": "Main Asset Number", "SUB_NUMBER": "Asset Subnumber", "LOC_BUS_AREA": "Business Area", "COSTCENTER": "Cost Center", "LOC_WBS_ELEM": "Work Breakdown Structure Element (WBS Element)", "STANDORDER": "Standing order number", "SETTLORDER": "Settlement order", "SALESORG": "Sales Organization", "DISTR_CHAN": "Distribution Channel", "DIVISION": "Division", "ORDPLANID": "Maintenance order planning indicator", "START_DATE": "Basic start date", "FINISH_DATE": "Basic finish date", "BASICSTART": "Basic start time", "BASIC_FIN": "Basic finish (time)", "PRIORITY": "Priority", "REVISION": "Revision for Plant Maintenance and Customer Service", "VERSION": "Version of Available Capacity", "SCHED_TYPE": "Scheduling type", "AUTOSCHED": "Indicator: Schedule automatically", "CAP_REQMTS": "Indicator: Calculate capacity requirements", "SCHEDULING_EXACT_BREAK_TIMES": "Indicator:  Sc<PERSON>uling allowing for breaks", "MRP_RELEVANT": "Reservation Relevance/Generation of Purchase Requisition", "SALES_ORD": "Sales Order Number", "S_ORD_ITEM": "Item Number in Sales Order", "CALC_MOTIVE": "Accounting Indicator", "INVEST_PROFILE": "Investment measure profile", "SCALE": "Scale of investment objects", "INV_REASON": "Reason for investment", "ENVIR_INVEST": "Reason for environmental investment", "ESTIMATED_COSTS": "Estimated total costs of order", "CURRENCY": "Order Currency", "CURRENCY_ISO": "ISO currency code", "CSTG_SHEET": "Costing Sheet", "OVERHEAD_KEY": "Overhead key", "RES_ANAL_KEY": "Results Analysis Key", "SHORT_TEXT": "Description", "ASSEMBLY_EXTERNAL": "Long Material Number for Field ASSEMBLY", "ASSEMBLY_GUID": "External GUID for ASSEMBLY Field", "ASSEMBLY_VERSION": "Version Number for ASSEMBLY Field", "MATERIAL_EXTERNAL": "Long Material Number for MATERIAL Field", "MATERIAL_GUID": "External GUID for MATERIAL Field", "MATERIAL_VERSION": "Version Number for MATERIAL Field", "NOTIF_NO": "Notification No", "TECO_REF_DATE": "PM Order: Reference Date", "TECO_REF_TIME": "Time of Reference Date", "CALID": "Factory Calendar", "KALSN": "Calendar Selection for Order", "SUPERIOR_ORDERID": "Number of superior order", "NOTIF_TYPE": "Notification Type", "START_POINT": "Start Point", "END_POINT": "End Point", "LINEAR_LENGTH": "Length", "LINEAR_UNIT": "Unit of Measurement for Linear Data", "LINEAR_UNIT_ISO": "Unit of Measurement for Linear Data in ISO Code", "FIRST_OFFSET_TYPE_CODE": "Type of First Offset", "FIRST_OFFSET_VALUE": "Value of Offset 1", "FIRST_OFFSET_UNIT": "Unit of Measurement for Offset 1", "FIRST_OFFSET_UNIT_ISO": "Unit of Measurement for Offset 1 in ISO Code", "SECOND_OFFSET_TYPE_CODE": "Type of Second Offset", "SECOND_OFFSET_VALUE": "Value of Offset 2", "SECOND_OFFSET_UNIT": "Unit of Measurement for Offset 2", "SECOND_OFFSET_UNIT_ISO": "Unit of Measurement for Offset 2 in ISO Code", "MARKER_START_POINT": "Marker for Start Point", "MARKER_DISTANCE_START_POINT": "Distance between Marker and Start Point", "MARKER_END_POINT": "Marker for End Point", "MARKER_DISTANCE_END_POINT": "Length Spec for Distance between Marker and End Point", "MARKER_DISTANCE_UNIT": "Unit for the Distance from Marker", "MARKER_DISTANCE_UNIT_ISO": "Unit for the Distance from Marker in ISO Code", "REFERENCE_ORDER": "Reference order number", "USERSTATUS": "Header User Status"}, "IT_HEADER_SRV": {"OBJECT_NO": "Object number", "SALESORG": "Sales Organization", "DISTR_CHAN": "Distribution Channel", "DIVISION": "Division", "SALES_GRP": "Sales Group", "SALES_OFF": "Sales Office", "PURCH_NO_C": "Customer purchase order number", "PURCH_DATE": "Customer purchase order date", "MATERIAL": "Service product", "QUANTITY": "Service product quantity", "BASE_UOM": "Base Unit of Measure", "BASE_UOM_ISO": "ISO code for unit of measurement", "CONFIGURATION": "Configuration", "DLI_PROFILE": "Dynamic Item Processor Profile", "BILLING_FORM": "Billing form", "MATERIAL_EXTERNAL": "Long Material Number for MATERIAL Field", "MATERIAL_GUID": "External GUID for MATERIAL Field", "MATERIAL_VERSION": "Version Number for MATERIAL Field"}}, "IT_PARTNER": [{"ORDERID": "Order Number", "PARTN_ROLE": "Partner Function", "PARTN_ROLE_OLD": "Partner Function", "PARTNER": "Partner", "PARTNER_OLD": "Partner"}], "OPERATIONS": [{"IT_OPERATION": {"ACTIVITY": "Operation/Activity Number", "SUB_ACTIVITY": "Suboperation", "CONTROL_KEY": "Control key", "WORK_CNTR": "Work center", "PLANT": "Plant", "STANDARD_TEXT_KEY": "Standard text key", "DESCRIPTION": "Operation short text", "LANGU": "Language Key", "LANGU_ISO": "2-Character SAP Language Code", "NO_OF_TIME_TICKETS": "Number of Time Tickets", "WAGETYPE": "Wage Type", "SUITABILITY": "Suitability", "WAGEGROUP": "Wage group", "SORT_FLD": "Sort field", "VENDOR_NO": "Account Number of <PERSON><PERSON>or or Creditor", "QUANTITY": "Operation quantity in order unit of measure", "BASE_UOM": "Base Unit of Measure", "BASE_UOM_ISO": "ISO code for unit of measurement", "PRICE": "Price", "PRICE_UNIT": "Price Unit", "COST_ELEMENT": "Cost Element", "CURRENCY": "<PERSON><PERSON><PERSON><PERSON> Key", "CURRENCY_ISO": "ISO currency code", "INFO_REC": "Number of Purchasing Info Record", "PURCH_ORG": "Purchasing Organization", "PUR_GROUP": "Purchasing group for external processing", "MATL_GROUP": "Material Group", "AGREEMENT": "Number of Principal Purchase Agreement", "AGMT_ITEM": "Item Number of Principal Purchase Agreement", "PREQ_NAME": "Name of Requisitioner/Requester", "TRACKINGNO": "Requirement Tracking Number", "NUMBER_OF_CAPACITIES": "Number of capacities required", "PERCENT_OF_WORK": "Work percentage", "CALC_KEY": "Key for calculation", "ACTTYPE": "Activity Type", "SYSTCOND": "Syst.Condition", "ASSEMBLY": "Assembly", "INT_DISTR": "Distr.cap.reqmts (plant maint.,process order, network)", "GR_RCPT": "Goods Recipient/Ship-To Party", "UNLOAD_PT": "Unloading Point", "PERS_NO": "Personnel number", "FW_ORDER": "Framework Order", "ORDER_ITEM": "Item of framework order", "PLND_DELRY": "Planned Delivery Time in Days", "DURATION_NORMAL": "Normal duration of the activity", "DURATION_NORMAL_UNIT": "Normal duration/unit", "DURATION_NORMAL_UNIT_ISO": "ISO code for unit of measurement", "CONSTRAINT_TYPE_START": "Constraint on the basic start date for the activity", "CONSTRAINT_TYPE_FINISH": "Constraint on the finish date of the activity", "WORK_ACTIVITY": "Work involved in the activity", "UN_WORK": "Unit for work", "UN_WORK_ISO": "ISO code for unit of measurement", "START_CONS": "Constraint for activity start (Basic)", "STRTTIMCON": "Constraint for activity start time (Basic)", "FIN_CONSTR": "Constraint for finish of activity (Basic)", "FINTIMCONS": "Basic finish time of the activity", "EXECFACTOR": "Execution Factor", "MRP_RELEVANT": "Reservation Relevance/Generation of Purchase Requisition", "FIELD_KEY": "Key word ID for user-defined fields", "USR00": "User field with 20 characters", "USR01": "User field with 20 characters", "USR02": "User field with 10 characters", "USR03": "User field with 10 characters", "USR04": "User field for quantity (length 10.3)", "USE04": "User field: Unit for quantity fields", "USE04_ISO": "ISO code for unit of measurement", "USR05": "User field for quantity (length 10.3)", "USE05": "User field: Unit for quantity fields", "USE05_ISO": "ISO code for unit of measurement", "USR06": "User-defined field for values (length 10,3)", "USE06": "User field: Unit for value fields", "USE06_ISO": "ISO currency code", "USR07": "User-defined field for values (length 10,3)", "USE07": "User field: Unit for value fields", "USE07_ISO": "ISO currency code", "USR08": "User field for date", "USR09": "User field for date", "USR10": "User-defined field: Indicator for reports", "USR11": "User-defined field: Indicator for reports", "ASSEMBLY_EXTERNAL": "Long Material Number for Field ASSEMBLY", "ASSEMBLY_GUID": "External GUID for ASSEMBLY Field", "ASSEMBLY_VERSION": "Version Number for ASSEMBLY Field", "EQUIPMENT": "Equipment Number", "FUNCT_LOC": "Functional Location Label", "SUBCONTRACTING": "Indicator: Externally processed op. with subcontracting", "START_POINT": "Start Point", "END_POINT": "End Point", "LINEAR_LENGTH": "Length", "LINEAR_UNIT": "Unit of Measurement for Linear Data", "LINEAR_UNIT_ISO": "Unit of Measurement for Linear Data in ISO Code", "FIRST_OFFSET_TYPE_CODE": "Type of First Offset", "FIRST_OFFSET_VALUE": "Value of Offset 1", "FIRST_OFFSET_UNIT": "Unit of Measurement for Offset 1", "FIRST_OFFSET_UNIT_ISO": "Unit of Measurement for Offset 1 in ISO Code", "SECOND_OFFSET_TYPE_CODE": "Type of Second Offset", "SECOND_OFFSET_VALUE": "Value of Offset 2", "SECOND_OFFSET_UNIT": "Unit of Measurement for Offset 2", "SECOND_OFFSET_UNIT_ISO": "Unit of Measurement for Offset 2 in ISO Code", "MARKER_START_POINT": "Marker for Start Point", "MARKER_DISTANCE_START_POINT": "Distance between Marker and Start Point", "MARKER_END_POINT": "Marker for End Point", "MARKER_DISTANCE_END_POINT": "Length Spec for Distance between Marker and End Point", "MARKER_DISTANCE_UNIT": "Unit for the Distance from Marker", "MARKER_DISTANCE_UNIT_ISO": "Unit for the Distance from Marker in ISO Code", "MAINTENANCE_ACTIVITY_TYPE": "Maintenance activity type", "BUS_AREA": "Business Area", "WBS_ELEM": "Work breakdown structure element (WBS element)", "PROFIT_CTR": "Profit Center", "CSTG_SHEET": "Costing Sheet", "OVERHEAD_KEY": "Overhead key", "TAXJURCODE": "Tax Jurisdiction", "OBJECTCLASS": "Object Class", "FUNC_AREA": "Functional Area", "USERSTATUS": "Operation User Status", "ACTUAL_START_DATE": "Actual start: Execution (date)", "ACTUAL_START_TIME": "Actual start: Execution/setup (time)", "ACTUAL_FINISH_DATE": "Actual finish: Execution (date)", "ACTUAL_FINISH_TIME": "Actual finish: Execution (time)"}, "CHARACTERISTICS": {"CHARACTERISTIC_NAME": "Characteristic Value"}}], "IT_TASKLISTS": [{"TASK_LIST_TYPE": "Task List Type", "TASK_LIST_GROUP": "Key for Task List Group", "GROUP_COUNTER": "Group Counter", "DELETE_OLD_OPERATIONS": "Delete Existing Operations When Integrating Task List", "WORKCENTER_SEL": "Work Center for Operation Selection On Integrating Task List", "PLANT_SEL": "Plant for Work Center for Operation Selection", "WORKCENTER_USE": "Work Center to be Used for Task List Integration", "PLANT_USE": "Work to be Used for Task List Integration", "USE_WORKCENTER_FROM_HEAD": "Use Work Center from Order Header in Operations"}]}