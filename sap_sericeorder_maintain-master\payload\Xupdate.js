"use strict";

module.exports = new Object({
  IT_HEADER_UP: {
    ORDERID: "",
    BUS_AREA: "X",
    MN_WK_CTR: "X",
    PLANT: "X",
    PMACTTYPE: "X",
    PLANGROUP: "X",
    SYSTCOND: "X",
    FUNCT_LOC: "X",
    EQUIPMENT: "X",
    SERIALNO: "X",
    MATERIAL: "X",
    ASSEMBLY: "X",
    DEVICEDATA: "X",
    MAINTPLANT: "X",
    LOCATION: "X",
    MAINTROOM: "X",
    PLSECTN: "X",
    LOC_WK_CTR: "X",
    ABCINDIC: "X",
    SORTFIELD: "X",
    PROFIT_CTR: "X",
    RESPCCTR: "X",
    FUNC_AREA: "X",
    SUPERIOR_NETWORK: "X",
    SUPERIOR_ACTIVITY: "X",
    WBS_ELEM: "X",
    PROCESSING_GROUP: "X",
    TAXJURCODE: "X",
    LOC_COMP_CODE: "X",
    ASSET_NO: "X",
    SUB_NUMBER: "X",
    LOC_BUS_AREA: "X",
    COSTCENTER: "X",
    LOC_WBS_ELEM: "X",
    STANDORDER: "X",
    SETTLORDER: "X",
    SALESORG: "X",
    DISTR_CHAN: "X",
    DIVISION: "X",
    ORDPLANID: "X",
    START_DATE: "X",
    FINISH_DATE: "X",
    BASICSTART: "X",
    BASIC_FIN: "X",
    PRIORITY: "X",
    REVISION: "X",
    VERSION: "X",
    SCHED_TYPE: "X",
    AUTOSCHED: "X",
    CAP_REQMTS: "X",
    SCHEDULING_EXACT_BREAK_TIMES: "X",
    MRP_RELEVANT: "X",
    SALES_ORD: "X",
    S_ORD_ITEM: "X",
    CALC_MOTIVE: "X",
    INVEST_PROFILE: "X",
    SCALE: "X",
    INV_REASON: "X",
    ENVIR_INVEST: "X",
    ESTIMATED_COSTS: "X",
    CURRENCY: "X",
    CSTG_SHEET: "X",
    OVERHEAD_KEY: "X",
    RES_ANAL_KEY: "X",
    SHORT_TEXT: "X",
    NOTIF_NO: "X",
    CALID: "X",
    KALSN: "X",
    SUPERIOR_ORDERID: "X",
    NOTIF_TYPE: "X",
    START_POINT: "X",
    END_POINT: "X",
    LINEAR_LENGTH: "X",
    LINEAR_UNIT: "X",
    LINEAR_UNIT_ISO: "X",
    FIRST_OFFSET_TYPE_CODE: "X",
    FIRST_OFFSET_VALUE: "X",
    FIRST_OFFSET_UNIT: "X",
    FIRST_OFFSET_UNIT_ISO: "X",
    SECOND_OFFSET_TYPE_CODE: "X",
    SECOND_OFFSET_VALUE: "X",
    SECOND_OFFSET_UNIT: "X",
    SECOND_OFFSET_UNIT_ISO: "X",
    MARKER_START_POINT: "X",
    MARKER_DISTANCE_START_POINT: "X",
    MARKER_END_POINT: "X",
    MARKER_DISTANCE_END_POINT: "X",
    MARKER_DISTANCE_UNIT: "X",
    MARKER_DISTANCE_UNIT_ISO: "X"
  },
  IT_HEADER_SRV_UP: {
    SALESORG: "X",
    DISTR_CHAN: "X",
    DIVISION: "X",
    SALES_GRP: "X",
    SALES_OFF: "X",
    PURCH_NO_C: "X",
    PURCH_DATE: "X",
    MATERIAL: "X",
    QUANTITY: "X",
    BASE_UOM: "X",
    BASE_UOM_ISO: "X",
    DLI_PROFILE: "X",
    BILLING_FORM: "X"
  },
  IT_PARTNER_UP: {
    ORDERID: "",
    PARTN_ROLE: "X",
    PARTN_ROLE_OLD: "",
    PARTNER: "X",
    PARTNER_OLD: ""
  },
  IT_OPERATION_UP: {
    ACTIVITY: "X",
    SUB_ACTIVITY: "X",
    CONTROL_KEY: "X",
    WORK_CNTR: "X",
    PLANT: "X",
    STANDARD_TEXT_KEY: "X",
    DESCRIPTION: "X",
    LANGU: "X",
    LANGU_ISO: "X",
    NO_OF_TIME_TICKETS: "X",
    WAGETYPE: "X",
    SUITABILITY: "X",
    WAGEGROUP: "X",
    SORT_FLD: "X",
    VENDOR_NO: "X",
    QUANTITY: "X",
    BASE_UOM: "X",
    BASE_UOM_ISO: "X",
    PRICE: "X",
    PRICE_UNIT: "X",
    COST_ELEMENT: "X",
    CURRENCY: "X",
    CURRENCY_ISO: "X",
    INFO_REC: "X",
    PURCH_ORG: "X",
    PUR_GROUP: "X",
    MATL_GROUP: "X",
    AGREEMENT: "X",
    AGMT_ITEM: "X",
    PREQ_NAME: "X",
    TRACKINGNO: "X",
    NUMBER_OF_CAPACITIES: "X",
    PERCENT_OF_WORK: "X",
    CALC_KEY: "X",
    ACTTYPE: "X",
    SYSTCOND: "X",
    ASSEMBLY: "X",
    INT_DISTR: "X",
    GR_RCPT: "X",
    UNLOAD_PT: "X",
    PERS_NO: "X",
    FW_ORDER: "X",
    ORDER_ITEM: "X",
    PLND_DELRY: "X",
    DURATION_NORMAL: "X",
    DURATION_NORMAL_UNIT: "X",
    DURATION_NORMAL_UNIT_ISO: "X",
    CONSTRAINT_TYPE_START: "X",
    CONSTRAINT_TYPE_FINISH: "X",
    WORK_ACTIVITY: "X",
    UN_WORK: "X",
    UN_WORK_ISO: "X",
    START_CONS: "X",
    STRTTIMCON: "X",
    FIN_CONSTR: "X",
    FINTIMCONS: "X",
    EXECFACTOR: "X",
    MRP_RELEVANT: "X",
    FIELD_KEY: "X",
    USR00: "X",
    USR01: "X",
    USR02: "X",
    USR03: "X",
    USR04: "X",
    USE04: "X",
    USE04_ISO: "X",
    USR05: "X",
    USE05: "X",
    USE05_ISO: "X",
    USR06: "X",
    USE06: "X",
    USE06_ISO: "X",
    USR07: "X",
    USE07: "X",
    USE07_ISO: "X",
    USR08: "X",
    USR09: "X",
    USR10: "X",
    USR11: "X",
    EQUIPMENT: "X",
    FUNCT_LOC: "X",
    SUBCONTRACTING: "X",
    START_POINT: "X",
    END_POINT: "X",
    LINEAR_LENGTH: "X",
    LINEAR_UNIT: "X",
    LINEAR_UNIT_ISO: "X",
    FIRST_OFFSET_TYPE_CODE: "X",
    FIRST_OFFSET_VALUE: "X",
    FIRST_OFFSET_UNIT: "X",
    FIRST_OFFSET_UNIT_ISO: "X",
    SECOND_OFFSET_TYPE_CODE: "X",
    SECOND_OFFSET_VALUE: "X",
    SECOND_OFFSET_UNIT: "X",
    SECOND_OFFSET_UNIT_ISO: "X",
    MARKER_START_POINT: "X",
    MARKER_DISTANCE_START_POINT: "X",
    MARKER_END_POINT: "X",
    MARKER_DISTANCE_END_POINT: "X",
    MARKER_DISTANCE_UNIT: "X",
    MARKER_DISTANCE_UNIT_ISO: "X",
    MAINTENANCE_ACTIVITY_TYPE: "X",
    BUS_AREA: "X",
    WBS_ELEM: "X",
    PROFIT_CTR: "X",
    CSTG_SHEET: "X",
    OVERHEAD_KEY: "X",
    TAXJURCODE: "X",
    OBJECTCLASS: "X",
    FUNC_AREA: "X"
  }
});
