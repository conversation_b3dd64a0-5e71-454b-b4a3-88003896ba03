"use strict";

require("dotenv").config();
const http = require("http");
const express = require("express");
const logger = require("morgan");
const session = require("express-session");
const cookieParser = require("cookie-parser");
const bodyParser = require("body-parser");
const errorHandler = require("errorhandler");
const helmet = require("helmet");
const almorder = require("./routes/almorder");
const app = express();

// all environments
app.set("port", process.env.PORT || 3000);
app.use(logger("dev"));
app.use(
  session({
    resave: true,
    saveUninitialized: true,
    secret: process.env.APISECRET,
  })
);
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(cookieParser());
app.use(helmet());

app.get("/", (req, res) => {
  res.status(200).send("Service Order APIs");
});

// Service Order
app.use("/almorder", almorder);

// error handling middleware should be loaded after the loading the routes
if (app.get("env") === "development") {
  app.use(errorHandler());
}

// Initiate Server : Port
const server = http.createServer(app);
server.listen(app.get("port"), () => {
  console.log("Express server listening on port " + app.get("port"));
});
