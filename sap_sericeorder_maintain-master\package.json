{"name": "sapalmorders", "version": "1.0.0", "description": "Service Order Update API", "main": "app.js", "scripts": {"dev": "nodemon app.js", "start": "node app.js"}, "keywords": [], "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "ISC", "dependencies": {"@hapi/joi": "^17.1.1", "@hapi/joi-date": "^2.0.1", "basic-auth": "^2.0.1", "body-parser": "^1.20.2", "cookie-parser": "^1.4.6", "errorhandler": "^1.5.1", "express": "^4.18.2", "express-jwt": "^8.4.1", "express-session": "^1.17.3", "helmet": "^6.0.1", "jwks-rsa": "^3.0.1", "morgan": "^1.10.0", "node-rfc": "^3.3.1"}, "devDependencies": {"dotenv": "^16.0.3", "nodemon": "^3.1.9"}}