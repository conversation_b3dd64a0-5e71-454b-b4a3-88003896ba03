"use strict";

require("dotenv").config();
const crypto = require("crypto");
const rfcClient = require("node-rfc").Client;
const SchemaJoi = require("../schema/RfcSchema");
const Xupdate = require("../payload/Xupdate");
const fs = require("fs");
let StkChar = null;

// Convert SAP String(Number) to Number
const convertnumber = (str) => {
  return (str.at(-1) === '-') ? (Number(str.split('-')[0]) * -1) : (Number(str));
}

module.exports = class sapClient extends rfcClient {
  constructor(req) {
    super(req.session.credentials);
    //In case of JWT session is not required & hence destroied
    if (
      req.headers.authorization &&
      req.headers.authorization.split(" ")[0] === "Bearer"
    ) {
      req.session.destroy();
    }
  }

  clear_cache(StkChar) {
    //Clear chache if there is any change in file
    return new Promise((resolve) => {
      StkChar = null;
      resolve({
        message:
          "Standard Text Key, Characteristic & Longtext Mapping Updated!",
      });
    });
  }

  get_ZLP_SORD_LAY_AT() {
    return new Promise((resolve, reject) => {
      // Retrieve Standard-text-Key & Characteristic Relationship
      this.call("RFC_READ_TABLE", {
        QUERY_TABLE: "ZLP_SORD_LAY_AT",
        DELIMITER: "|",
      })
        .then((retData) => {
          const arStkChar = {};
          retData.DATA.forEach((row) => {
            const WA = row.WA.split("|");
            arStkChar[WA[1].trim() + "." + WA[2].trim()] = WA[4].trim();
          });
          try {
            fs.writeFileSync("payload/StkChar.json", JSON.stringify(arStkChar));
            resolve({
              message:
                "Standard Text Key, Characteristic & Longtext Mapping Updated!",
            });
          } catch (err) {
            reject(err);
          }
        })
        .catch((err) => reject(err));
    });
  }

  get_STK_Char_Validation(payload) {
    //Validate Query Parameter ORDERID
    return new Promise((resolve, reject) => {
      const result = SchemaJoi.StkCharQuery.validate(payload);
      if (result.hasOwnProperty("error")) {
        reject(result.error.details[0].message);
      } else {
        resolve(payload);
      }
    });
  }

  get_maintain_payload() {
    return new Promise((resolve, reject) => {
      fs.readFile("payload/update.json", "utf-8", (err, data) => {
        if (err) reject(err);
        resolve(JSON.parse(data));
      });
    });
  }

  get_STK_Char_Mapping(query) {
    //Filter by Payload/Query parameter
    return new Promise((resolve, reject) => {
      if (StkChar === null)
        StkChar = JSON.parse(fs.readFileSync("payload/StkChar.json"));
      let CharacFiltr = null;
      if (query.StanderdTextKey && query.Characteristic) {
        CharacFiltr = Object.keys(StkChar).filter(
          (e) =>
            e.startsWith(query.StanderdTextKey) &&
            e.endsWith(query.Characteristic)
        );
      } else if (query.StanderdTextKey && !query.Characteristic) {
        CharacFiltr = Object.keys(StkChar).filter((e) =>
          e.startsWith(query.StanderdTextKey)
        );
      } else if (!query.StanderdTextKey && query.Characteristic) {
        CharacFiltr = Object.keys(StkChar).filter((e) =>
          e.endsWith(query.Characteristic)
        );
      } else if (!query.StanderdTextKey && !query.Characteristic) {
        CharacFiltr = Object.keys(StkChar);
      }
      //
      if (CharacFiltr.length > 0) {
        const finalData = [];
        CharacFiltr.forEach((e) => {
          finalData.push({
            StanderdTextKey: e.split(".")[0],
            CharacteristicName: e.split(".")[1],
            valueType: StkChar[e],
          });
        });
        resolve(finalData);
      } else {
        reject("No data found!");
      }
    });
  }

  alm_tasklist_validation(payload) {
    //Validate Query Parameter ORDERID
    return new Promise((resolve, reject) => {
      const result = SchemaJoi.TaskListQuery.validate(payload);
      if (result.hasOwnProperty("error")) {
        reject(result.error.details[0].message);
      } else {
        resolve(payload);
      }
    });
  }

  // RFC-Query for TaskList Details
  async GetTaskList(QueryParms) {
    return new Promise((resolve, reject) => {
      this.call("ZSD_ALM_TASKLIST", {
        IV_PLNTY: String(QueryParms.TaskListType),
        IV_PLNNR: String(("00000000" + QueryParms.TaskListGroupKey).slice(-8)),
        IV_PLNAL: String(("00" + QueryParms.GroupCounter).slice(-2)),
        IS_OBJ_SEL: { FLG_OPR: "X" },
      })
        .then(
          (almdata) =>
            new Promise((resolve) => resolve(JSON.parse(almdata.TASKLISTDATA)))
        )
        .then((almdata) => resolve(almdata))
        .catch((error) => reject(error));
    });
  }

  alm_orderid_validation(payload) {
    //Validate Query Parameter ORDERID
    return new Promise((resolve, reject) => {
      const result = SchemaJoi.OrderQuery.validate(payload);
      if (result.hasOwnProperty("error")) {
        reject(result.error.details[0].message);
      } else {
        resolve(payload);
      }
    });
  }

  get_Alm_order_details(payload) {
    return new Promise((resolve, reject) => {
      this.call("ZSD_ALM_ORDER_DETAILS", {
        ORDERID: ("000000000000" + payload.OrderID).slice(-12),
        LTEXT: payload.Ltext,
      })
        .then((almdata) => this.parse_string_toJson(almdata))
        .then((almdata) => this.prepare_payload(almdata))
        .then((almdata) => resolve(almdata))
        .catch((error) => reject(error));
    });
  }

  parse_string_toJson(almdata) {
    return new Promise(async (resolve, reject) => {
      try {
        const jsonData = await JSON.parse(almdata.SDALMDATA);
        resolve(jsonData);
      } catch (error) {
        reject(error);
      }
    })
  }

  prepare_payload(almdata) {
    return new Promise((resolve) => {
      const payload = {
        HEADER: { IT_HEADER: {}, IT_HEADER_SRV: {} },
        PARTNERS: [],
        OPERATIONS: [],
      };
      payload.HEADER.IT_HEADER = almdata.ALM_HEADER;
      payload.HEADER.IT_HEADER_SRV = almdata.ALM_SRVDATA;
      payload.PARTNERS = almdata.ALM_PARTNER;

      //Prepare Characteristics
      almdata.ALM_OPERATIONS.forEach((operation) => {
        if (StkChar === null)
          StkChar = JSON.parse(fs.readFileSync("payload/StkChar.json"));
        const characteristics = {};
        Object.keys(StkChar)
          .filter((key) => key.startsWith(operation.STANDARD_TEXT_KEY))
          .forEach((STKcharac) => {
            const chrac = STKcharac.replace(
              operation.STANDARD_TEXT_KEY + ".",
              ""
            );
            const rfcCharc = almdata.SD_CFGS_CUVALS.find(
              (c) => c.CHARC === chrac.trim()
            );
            if (rfcCharc) {
              characteristics[chrac] = rfcCharc.VALUE;
              if (StkChar[STKcharac] === "LTEXT") {
                const ltext = almdata.TDLTEXT.find(
                  (txt) => txt.TDHEAD.TDNAME === rfcCharc.VALUE
                );
                if (ltext) {
                  let char_ltext = "";
                  ltext.TDLINE.forEach((e) => {
                    if (
                      e.hasOwnProperty("TDFORMAT") &&
                      e.TDFORMAT === "*" &&
                      char_ltext != ""
                    ) {
                      char_ltext = char_ltext.concat(" \n ", e.TDLINE);
                    } else {
                      char_ltext = char_ltext.concat(e.TDLINE);
                    }
                  });
                  characteristics[chrac] = char_ltext;
                  char_ltext = "";
                }
              } else {
                if (
                  characteristics[chrac] !== "" &&
                  characteristics[chrac].search("E+") > 0
                ) {
                  characteristics[chrac] = Number(characteristics[chrac]);
                }
              }
            } else {
              characteristics[chrac] = "";
            }
          });
        payload.OPERATIONS.push({
          IT_OPERATION: operation,
          CHARACTERISTICS: characteristics,
        });
      });
      resolve(payload);
    });
  }

  alm_payload_validation(payload) {
    //Validate input payload structure
    return new Promise((resolve, reject) => {
      const result = SchemaJoi.OrderMaintain.validate(payload);
      if (result.hasOwnProperty("error")) {
        reject(result.error.details[0].message);
      } else {
        resolve(payload);
      }
    });
  }

  get_ref_almdata(payload) {
    return new Promise((resolve, reject) => {
      payload.HEADER.IT_HEADER.ORDERID = String(
        ("000000000000" + payload.HEADER.IT_HEADER.ORDERID).slice(-12)
      );
      //Retrieve Sales & Service Order Informations
      this.call("ZSD_ALM_ORDER_DETAILS", {
        ORDERID: payload.HEADER.IT_HEADER.ORDERID,
      })
        .then(
          (almdata) =>
            new Promise((resolve) => resolve(JSON.parse(almdata.SDALMDATA)))
        )
        .then((almdata) => {
          if (almdata["RETURN"].length > 0) {
            reject(almdata["RETURN"]);
          } else {
            payload["ALM_HEADER"] = almdata.ALM_HEADER;
            payload["ALM_OPERATIONS"] = almdata.ALM_OPERATIONS;
            payload["ALM_MAXACTIVITY"] = almdata.ALM_MAXACTIVITY;
            payload["SD_ORDER_HEADER"] = almdata.SD_ORDER_HEADER;
            payload["SD_ORDER_ITEM"] = almdata.SD_ORDER_ITEM;
            payload["SD_CFGS_CUREFS"] = almdata.SD_CFGS_CUREFS;
            payload["SD_CFGS_CUCFGS"] = almdata.SD_CFGS_CUCFGS;
            payload["SD_CFGS_CUINS"] = almdata.SD_CFGS_CUINS;
            payload["ORDER_CFGS_VALUE"] = almdata.SD_CFGS_CUVALS;
            resolve(payload);
          }
        })
        .catch((error) => reject(error));
    });
  }

  Prepare_Bapi_Export(almdata) {
    return new Promise((resolve, reject) => {
      //Prepare Final Payload for the BAPI/Rfc
      this.Prepare_alm_HEADER(almdata)
        .then((retndata) => this.Prepare_alm_TASKLISTS(retndata))
        .then((retndata) => this.Prepare_alm_PARTNER(retndata))
        .then((retndata) => this.Prepare_alm_OPERATIONS(retndata))
        .then((retndata) => this.Prepare_alm_CHARACTERISTICS(retndata))
        .then((retndata) => resolve(retndata))
        .catch((error) => reject(error));
    });
  }

  Prepare_alm_HEADER(almdata) {
    return new Promise((resolve) => {
      almdata["IT_METHODS"] = [];
      almdata["IT_USERSTATUS_REF"] = 1;

      // Prepare BAPI data from HEADER
      if (almdata.hasOwnProperty("HEADER")) {
        if (almdata.HEADER.hasOwnProperty("IT_HEADER")) {
          //System Status
          if (almdata.HEADER.IT_HEADER.hasOwnProperty("TECHNICALCOMPLETE")) {
            if (almdata.HEADER.IT_HEADER.TECHNICALCOMPLETE === true) {
              almdata["IT_METHODS"].push({
                REFNUMBER: String(almdata.IT_USERSTATUS_REF),
                OBJECTTYPE: "HEADER",
                METHOD: "TECHNICALCOMPLETE",
                OBJECTKEY: almdata.HEADER.IT_HEADER.ORDERID,
              });
              almdata.IT_USERSTATUS_REF++;
            }
            delete almdata.HEADER.IT_HEADER.TECHNICALCOMPLETE;
          }

          if (almdata.HEADER.IT_HEADER.hasOwnProperty("USERSTATUS")) {
            almdata["IT_USERSTATUS"] = [];
            let it_payload_header_userstatus =
              almdata.HEADER.IT_HEADER.USERSTATUS;

            //Check whether current userstatus exist in Payload USERSTATUS
            almdata.ALM_HEADER.USERSTATUS.split(" ").forEach((userstatus) => {
              if (userstatus != "") {
                const indx = it_payload_header_userstatus.indexOf(userstatus);
                if (indx >= 0) {
                  it_payload_header_userstatus =
                    it_payload_header_userstatus.replace(userstatus, "");
                } else {
                  almdata.IT_USERSTATUS.push({
                    USER_ST_TEXT: userstatus,
                    LANGU: "E",
                    INACTIVE: "X",
                    CHANGE_EVENT: "01",
                  });
                  almdata["IT_METHODS"].push({
                    REFNUMBER: String(almdata.IT_USERSTATUS_REF),
                    OBJECTTYPE: "USERSTATUS",
                    METHOD: "CHANGE",
                    OBJECTKEY: almdata.HEADER.IT_HEADER.ORDERID,
                  });
                  almdata.IT_USERSTATUS_REF++;
                }
              }
            });
            //Set Active Status
            it_payload_header_userstatus.split(" ").forEach((userstatus) => {
              if (userstatus != "") {
                almdata.IT_USERSTATUS.push({
                  USER_ST_TEXT: userstatus,
                  LANGU: "E",
                  CHANGE_EVENT: "01",
                });
                almdata["IT_METHODS"].push({
                  REFNUMBER: String(almdata.IT_USERSTATUS_REF),
                  OBJECTTYPE: "USERSTATUS",
                  METHOD: "CHANGE",
                  OBJECTKEY: almdata.HEADER.IT_HEADER.ORDERID,
                });
                almdata.IT_USERSTATUS_REF++;
              }
            });
            delete almdata.HEADER.IT_HEADER.USERSTATUS;
          }

          // Prepare BAPI data from IT_HEADER
          almdata["IT_HEADER"] = [almdata.HEADER.IT_HEADER];
          const it_header_up = {};
          Object.keys(almdata.HEADER.IT_HEADER).forEach((key) => {
            if (Xupdate.IT_HEADER_UP.hasOwnProperty(key)) {
              it_header_up[key] = !Xupdate.IT_HEADER_UP[key]
                ? almdata.HEADER.IT_HEADER.ORDERID
                : Xupdate.IT_HEADER_UP[key];
            }
          });
          almdata["IT_HEADER_UP"] = [it_header_up];
          almdata["IT_METHODS"].push({
            REFNUMBER: "1",
            OBJECTTYPE: "HEADER",
            METHOD: "CHANGE",
            OBJECTKEY: almdata.HEADER.IT_HEADER.ORDERID,
          });
        }

        // Prepare BAPI data from IT_HEADER_SRV
        if (almdata.HEADER.hasOwnProperty("IT_HEADER_SRV")) {
          almdata["IT_HEADER_SRV"] = [almdata.HEADER.IT_HEADER_SRV];
          const it_header_srv_up = {};
          Object.keys(almdata.HEADER.IT_HEADER_SRV).forEach((key) => {
            if (Xupdate.IT_HEADER_SRV_UP.hasOwnProperty(key)) {
              it_header_srv_up[key] = Xupdate.IT_HEADER_SRV_UP[key];
            }
          });
          almdata["IT_HEADER_SRV_UP"] = [it_header_srv_up];
        }

        delete almdata.HEADER;
      }
      resolve(almdata);
    });
  }

  Prepare_alm_TASKLISTS(almdata) {
    return new Promise((resolve) => {
      if (almdata.hasOwnProperty("IT_TASKLISTS")) {
        // if (!almdata["IT_METHODS"]) almdata["IT_METHODS"] = [];
        let IT_USERSTATUS_REF = 1;
        almdata.IT_TASKLISTS.forEach(e => {
          almdata["IT_METHODS"].push({
            REFNUMBER: String(IT_USERSTATUS_REF),
            OBJECTTYPE: "TASKLIST",
            METHOD: "ADD",
            OBJECTKEY: almdata.IT_HEADER[0].ORDERID,
          });
          IT_USERSTATUS_REF++;
        })
      }
      resolve(almdata);
    });
  }

  Prepare_alm_PARTNER(almdata) {
    return new Promise((resolve) => {
      if (almdata.hasOwnProperty("PARTNERS")) {
        almdata["IT_PARTNER"] = [];
        almdata["IT_PARTNER_UP"] = [];

        almdata.PARTNERS.forEach((partner) => {
          almdata.IT_PARTNER.push(partner);
          const it_partner_up = {};
          Object.keys(partner).forEach((key) => {
            if (Xupdate.IT_PARTNER_UP.hasOwnProperty(key)) {
              it_partner_up[key] = !Xupdate.IT_PARTNER_UP[key]
                ? partner[key]
                : Xupdate.IT_PARTNER_UP[key];
            }
          });
          if (!it_partner_up.ORDERID)
            it_partner_up["ORDERID"] = almdata.IT_HEADER[0].ORDERID;
          almdata.IT_PARTNER_UP.push(it_partner_up);
        });

        almdata["IT_METHODS"].push({
          REFNUMBER: "1",
          OBJECTTYPE: "PARTNER",
          METHOD: "CHANGE",
          OBJECTKEY: almdata.IT_HEADER[0].ORDERID,
        });
        delete almdata.PARTNERS;
      }
      resolve(almdata);
    });
  }

  Prepare_alm_OPERATIONS(almdata) {
    return new Promise((resolve, reject) => {
      if (almdata.hasOwnProperty("OPERATIONS")) {
        const orderID = String(almdata.IT_HEADER[0].ORDERID); //Service Order Number

        almdata.ALM_OPERATIONS.sort((a, b) => b.ACTIVITY - a.ACTIVITY); //Sort Operation Activity Descending
        let maxActyvity = Number(almdata.ALM_MAXACTIVITY); //Highest Activity Number
        delete almdata.ALM_MAXACTIVITY;
        let i = 0; //index or referance
        let txt_ref = 0; //index of text referance number
        let l_method = "CHANGE"; // Method for OPERATIONs Method table
        almdata["OPERATIONS"].forEach((operation) => {
          i++; //Increase by one
          // IT_OPERATIONS - Preparations
          if (operation.hasOwnProperty("IT_OPERATION")) {
            let no_stdtxtkey_upd = false;
            if (operation.IT_OPERATION.ACTIVITY) {
              operation.IT_OPERATION.ACTIVITY = (
                "0000" + String(operation.IT_OPERATION.ACTIVITY)
              ).slice(-4);
              const foundActivity = almdata.ALM_OPERATIONS.find(
                (o) => o.ACTIVITY === operation.IT_OPERATION.ACTIVITY
              );
              if (foundActivity) {
                if (!operation.IT_OPERATION.STANDARD_TEXT_KEY) {
                  operation.IT_OPERATION.STANDARD_TEXT_KEY =
                    foundActivity.STANDARD_TEXT_KEY;
                  no_stdtxtkey_upd = true;
                }
              } else {
                reject(
                  `Activity ${operation.IT_OPERATION.ACTIVITY} not found!!`
                );
              }
            } else {
              //Find Operations based on mandetory OPERATION fields
              const foundOperation = almdata.ALM_OPERATIONS.find(
                (o) =>
                  o.CONTROL_KEY === operation.IT_OPERATION.CONTROL_KEY &&
                  o.WORK_CNTR === operation.IT_OPERATION.WORK_CNTR &&
                  o.PLANT === operation.IT_OPERATION.PLANT &&
                  o.STANDARD_TEXT_KEY ===
                  operation.IT_OPERATION.STANDARD_TEXT_KEY &&
                  o.ACTTYPE === operation.IT_OPERATION.ACTTYPE &&
                  o.USR04 === Number(operation.IT_OPERATION.USR04)
              );
              if (foundOperation) {
                //If Operations found then it will be change Method
                operation.IT_OPERATION.ACTIVITY = foundOperation.ACTIVITY;
              } else {
                //If Operation Not found then Create Operation
                maxActyvity = maxActyvity + 1;
                operation.IT_OPERATION.ACTIVITY = (
                  "0000" + String(maxActyvity)
                ).slice(-4);
                l_method = "CREATE";
              }
            }

            //Check Payload Operations has USERSTATUS
            if (operation.IT_OPERATION.hasOwnProperty("USERSTATUS")) {
              if (!almdata["IT_USERSTATUS"]) almdata["IT_USERSTATUS"] = [];
              let it_operation_userstatus = operation.IT_OPERATION.USERSTATUS;
              const alm_operation = almdata.ALM_OPERATIONS.find(
                (e) => e.ACTIVITY === operation.IT_OPERATION.ACTIVITY
              );

              if (alm_operation) {
                //Set Inactive Status
                alm_operation.FIELD_USER_STATUS.split(" ").forEach(
                  (userstatus) => {
                    if (userstatus != "") {
                      //Check whether current userstatus exist in Payload USERSTATUS
                      const indx =
                        operation.IT_OPERATION.USERSTATUS.indexOf(userstatus);

                      if (indx >= 0) {
                        it_operation_userstatus =
                          it_operation_userstatus.replace(userstatus, "");
                      } else {
                        almdata.IT_USERSTATUS.push({
                          USER_ST_TEXT: userstatus,
                          INACTIVE: "X",
                          LANGU: "E",
                          CHANGE_EVENT: "01",
                        });
                        almdata["IT_METHODS"].push({
                          REFNUMBER: almdata.IT_USERSTATUS_REF,
                          OBJECTTYPE: "USERSTATUS",
                          METHOD: "CHANGE",
                          OBJECTKEY: orderID + operation.IT_OPERATION.ACTIVITY,
                        });
                        almdata.IT_USERSTATUS_REF++;
                      }
                    }
                  }
                );
              }

              // TIMETICKETS - For Time Confirmation API
              const timeconf = {};
              if (operation.IT_OPERATION.hasOwnProperty("ACTUAL_START_DATE")) {
                timeconf["EXEC_START_DATE"] =
                  operation.IT_OPERATION.ACTUAL_START_DATE;
                delete operation.IT_OPERATION.ACTUAL_START_DATE;
              }
              if (operation.IT_OPERATION.hasOwnProperty("ACTUAL_START_TIME")) {
                timeconf["EXEC_START_TIME"] =
                  operation.IT_OPERATION.ACTUAL_START_TIME;
                delete operation.IT_OPERATION.ACTUAL_START_TIME;
              }
              if (operation.IT_OPERATION.hasOwnProperty("ACTUAL_FINISH_DATE")) {
                timeconf["EXEC_FIN_DATE"] =
                  operation.IT_OPERATION.ACTUAL_FINISH_DATE;
                delete operation.IT_OPERATION.ACTUAL_FINISH_DATE;
              }
              if (operation.IT_OPERATION.hasOwnProperty("ACTUAL_FINISH_TIME")) {
                timeconf["EXEC_FIN_TIME"] =
                  operation.IT_OPERATION.ACTUAL_FINISH_TIME;
                delete operation.IT_OPERATION.ACTUAL_FINISH_TIME;
              }
              if (Object.keys(timeconf).length > 0) {
                if (
                  almdata.ALM_HEADER.SYS_STATUS.split(" ").find(
                    (e) => e === "REL"
                  )
                ) {
                  if (!almdata["TIMETICKETS"]) almdata["TIMETICKETS"] = [];
                  timeconf["ORDERID"] = orderID;
                  timeconf["OPERATION"] = operation.IT_OPERATION.ACTIVITY;
                  almdata.TIMETICKETS.push(timeconf);
                } else {
                  reject("Service Order Not released!!");
                }
              }

              //Set Active Status
              it_operation_userstatus.split(" ").forEach((userstatus) => {
                if (userstatus != "") {
                  almdata.IT_USERSTATUS.push({
                    USER_ST_TEXT: userstatus,
                    LANGU: "E",
                    CHANGE_EVENT: "01",
                  });
                  almdata["IT_METHODS"].push({
                    REFNUMBER: almdata.IT_USERSTATUS_REF,
                    OBJECTTYPE: "USERSTATUS",
                    METHOD: "CHANGE",
                    OBJECTKEY: orderID + operation.IT_OPERATION.ACTIVITY,
                  });
                  almdata.IT_USERSTATUS_REF++;
                }
              });
              delete operation.IT_OPERATION.USERSTATUS;
            }

            // Operation Long Text
            if (operation.IT_OPERATION.DESCRIPTION) {
              txt_ref = txt_ref + 1;
              if (!almdata["IT_TEXT"]) almdata["IT_TEXT"] = [];
              if (!almdata["IT_TEXT_LINES"]) almdata["IT_TEXT_LINES"] = [];
              //Retrieve the Last Line Saved for Text
              let lTextEnd =
                almdata.IT_TEXT.length > 0
                  ? Number(almdata.IT_TEXT[almdata.IT_TEXT.length - 1].TEXTEND)
                  : 0;
              let lTextStart = lTextEnd + 1; //Begin New Line
              //Split String on each line break
              operation.IT_OPERATION.DESCRIPTION.split("\n").forEach(
                (newline) => {
                  const len = newline.length;
                  let lenTextStart = 0;
                  while (lenTextStart <= len) {
                    almdata.IT_TEXT_LINES.push({
                      TDFORMAT: lenTextStart === 0 ? "*" : "",
                      //132 is the max characteristic limit
                      TDLINE: newline.slice(
                        lenTextStart,
                        lenTextStart + 132 > len ? len : lenTextStart + 132
                      ),
                    });
                    lenTextStart = lenTextStart + 132;
                    lTextEnd = lTextEnd + 1; // Line Count
                  }
                }
              );
              almdata.IT_TEXT.push({
                ORDERID: orderID,
                ACTIVITY: operation.IT_OPERATION.ACTIVITY,
                LANGU: operation.IT_OPERATION.LANGU
                  ? operation.IT_OPERATION.LANGU
                  : "E",
                TEXTSTART: String(lTextStart),
                TEXTEND: String(lTextEnd),
              });
              almdata.IT_METHODS.push({
                REFNUMBER: String(txt_ref),
                OBJECTTYPE: "TEXT",
                METHOD: l_method,
                OBJECTKEY: orderID + operation.IT_OPERATION.ACTIVITY,
              });
              if (operation.IT_OPERATION.DESCRIPTION.length > 40) {
                operation.IT_OPERATION.DESCRIPTION =
                  operation.IT_OPERATION.DESCRIPTION.slice(0, 40);
              }
            }

            //Prepare Operations
            if (!almdata["IT_OPERATION"]) almdata["IT_OPERATION"] = [];
            almdata.IT_OPERATION.push(operation.IT_OPERATION);

            // IT_OPERATIONS_UP - Preparations
            const it_operation_up = {};
            Object.keys(operation.IT_OPERATION).forEach((key) => {
              if (Xupdate.IT_OPERATION_UP.hasOwnProperty(key)) {
                if (key === "STANDARD_TEXT_KEY" && no_stdtxtkey_upd) {
                  //If Activity is in the payload but no Standard Text Key
                  it_operation_up[key] = ""; // No Update
                } else {
                  it_operation_up[key] = Xupdate.IT_OPERATION_UP[key];
                }
              }
            });
            if (!almdata["IT_OPERATION_UP"]) almdata["IT_OPERATION_UP"] = [];
            almdata.IT_OPERATION_UP.push(it_operation_up);

            // IT_OPERATIONS - METHOD - Preparations
            almdata.IT_METHODS.push({
              REFNUMBER: String(i),
              OBJECTTYPE: "OPERATION",
              METHOD: l_method,
              OBJECTKEY: orderID + operation.IT_OPERATION.ACTIVITY,
            });
          }

          // CHARACTERISTICS
          if (operation.hasOwnProperty("CHARACTERISTICS") && almdata.hasOwnProperty("SD_CFGS_CUREFS")) {
            almdata.SD_CHARACTERISTICS = true; //Set thisparameter for future Use
            // Check Standard-Text-Key & Characteristic Assignment Table
            if (StkChar === null)
              StkChar = JSON.parse(fs.readFileSync("payload/StkChar.json"));
            // Gothrough Each Characteristics
            Object.keys(operation.CHARACTERISTICS).forEach((charc) => {
              //Check Characteristic & Standard Text Key - Mapping
              if (
                StkChar[
                operation.IT_OPERATION.STANDARD_TEXT_KEY + "." + charc
                ] ||
                StkChar[
                operation.IT_OPERATION.STANDARD_TEXT_KEY + "." + charc
                ] === ""
              ) {
                //Ckeck Sales Order already has the Characteristic
                const characIndex = almdata.ORDER_CFGS_VALUE.map(
                  (e) => e.CHARC
                ).indexOf(charc);
                if (characIndex >= 0) {
                  if (
                    StkChar[
                    operation.IT_OPERATION.STANDARD_TEXT_KEY + "." + charc
                    ] == "LTEXT"
                  ) {
                    const TextID = almdata.ORDER_CFGS_VALUE[characIndex].VALUE; // Get existing unique-Text ID
                    if (!almdata.TDLTEXT) almdata.TDLTEXT = [];
                    almdata.TDLTEXT = almdata.TDLTEXT.concat(
                      this.Prepare_Rfc_LText(
                        TextID,
                        operation.CHARACTERISTICS[charc],
                        "",
                        charc
                      )
                    );
                  } else {
                    almdata.ORDER_CFGS_VALUE[characIndex].VALUE =
                      operation.CHARACTERISTICS[charc];
                  }
                } else {
                  if (
                    StkChar[
                    operation.IT_OPERATION.STANDARD_TEXT_KEY + "." + charc
                    ] == "LTEXT"
                  ) {
                    const TextID = crypto.randomBytes(12).toString("Hex"); // Get unique-Text ID
                    if (!almdata.TDLTEXT) almdata.TDLTEXT = [];
                    almdata.TDLTEXT = almdata.TDLTEXT.concat(
                      this.Prepare_Rfc_LText(
                        TextID,
                        operation.CHARACTERISTICS[charc],
                        "X",
                        charc
                      )
                    );
                    almdata.ORDER_CFGS_VALUE.push({
                      CONFIG_ID: almdata.SD_CFGS_CUREFS.CONFIG_ID,
                      INST_ID: almdata.SD_CFGS_CUREFS.INST_ID,
                      CHARC: charc,
                      VALUE: TextID,
                    });
                  } else {
                    almdata.ORDER_CFGS_VALUE.push({
                      CONFIG_ID: almdata.SD_CFGS_CUREFS.CONFIG_ID,
                      INST_ID: almdata.SD_CFGS_CUREFS.INST_ID,
                      CHARC: charc,
                      VALUE: operation.CHARACTERISTICS[charc],
                    });
                  }
                }
              } else {
                //Reject- If Relationship between Standard-Text-Key & Characteristic is not-correct
                reject(
                  `Incorrect Assignment: ${operation.IT_OPERATION.ACTIVITY}/${operation.IT_OPERATION.STANDARD_TEXT_KEY}/${charc}`
                );
              }
            });
          }
        });
      }
      delete almdata.IT_USERSTATUS_REF;
      delete almdata.OPERATIONS;
      delete almdata.ALM_MAXACTIVITY;
      delete almdata.ALM_OPERATIONS;
      delete almdata.ALM_STDTXTKEY;
      resolve(almdata);
    });
  }

  Prepare_Rfc_LText(tdid, ltext, insert, charc) {
    const lctxt = { TDHEAD: {}, TDLINE: [], INSERT: "", CHARC: "" };
    if (ltext.length > 0) {
      lctxt.INSERT = insert;
      lctxt.CHARC = charc;
      lctxt.TDHEAD["TDNAME"] = tdid;
      lctxt.TDHEAD["TDID"] = "ZNO"; //LP Defined Text Object
      lctxt.TDHEAD["TDSPRAS"] = "E"; //Language English
      lctxt.TDHEAD["TDOBJECT"] = "ZLP_SORD"; //LP Defined Text Object
      //Split String on each line break
      ltext.split("\n").forEach((newline) => {
        const l = newline.length;
        let s = 0;
        while (s <= l) {
          lctxt.TDLINE.push({
            TDFORMAT: s === 0 ? "*" : "",
            TDLINE: newline.slice(s, s + 132 > l ? l : s + 132), //132 is the max characteristic limit
          });
          s = s + 132;
        }
      });
    }
    return [lctxt];
  }

  Prepare_alm_CHARACTERISTICS(almdata) {
    return new Promise((resolve) => {
      if (almdata.SD_CHARACTERISTICS) {
        // ORDER Header
        almdata["SALESDOCUMENT"] = almdata["SD_ORDER_HEADER"]["DOC_NUMBER"];
        almdata["ORDER_HEADER_INX"] = { UPDATEFLAG: "U" };

        // Order Item
        almdata["ORDER_ITEM_IN"] = [
          {
            ITM_NUMBER: (
              "000000" + String(almdata["SD_ORDER_ITEM"]["ITM_NUMBER"])
            ).slice(-6),
            PO_ITM_NO: almdata["SD_ORDER_ITEM"]["PO_ITM_NO"],
            MATERIAL: almdata["SD_ORDER_ITEM"]["MATERIAL"],
            PLANT: almdata["SD_ORDER_ITEM"]["PLANT"],
          },
        ];

        //Characteristic - Sales Order Configuration Details
        almdata["ORDER_CFGS_REF"] = [
          {
            POSEX: almdata.SD_CFGS_CUREFS.POSEX,
            CONFIG_ID: almdata.SD_CFGS_CUREFS.CONFIG_ID,
            ROOT_ID: almdata.SD_CFGS_CUCFGS.ROOT_ID,
            SCE: almdata.SD_CFGS_CUCFGS.SCE,
            COMPLETE: almdata.SD_CFGS_CUCFGS.COMPLETE,
            CONSISTENT: almdata.SD_CFGS_CUCFGS.CONSISTENT,
            CBASE_ID_TYPE: almdata.SD_CFGS_CUCFGS.CBASE_ID_TYPE,
          },
        ];

        almdata["ORDER_CFGS_INST"] = [
          {
            CONFIG_ID: almdata.SD_CFGS_CUINS.CONFIG_ID,
            INST_ID: almdata.SD_CFGS_CUINS.INST_ID,
            OBJ_TYPE: almdata.SD_CFGS_CUINS.OBJ_TYPE,
            CLASS_TYPE: almdata.SD_CFGS_CUINS.CLASS_TYPE,
            OBJ_KEY: almdata.SD_CFGS_CUINS.OBJ_KEY,
            OBJ_TXT: almdata.SD_CFGS_CUINS.OBJ_TXT,
            QUANTITY: almdata.SD_CFGS_CUINS.QUANTITY.trim(),
            AUTHOR: almdata.SD_CFGS_CUINS.AUTHOR,
            QUANTITY_UNIT: almdata.SD_CFGS_CUINS.QUANTITY_UNIT,
            COMPLETE: almdata.SD_CFGS_CUINS.COMPLETE,
            CONSISTENT: almdata.SD_CFGS_CUINS.CONSISTENT,
            OBJECT_GUID: almdata.SD_CFGS_CUINS.OBJECT_GUID,
            PERSIST_ID: almdata.SD_CFGS_CUINS.PERSIST_ID,
            PERSIST_ID_TYPE: almdata.SD_CFGS_CUINS.PERSIST_ID_TYPE,
          },
        ];

        delete almdata.SD_CHARACTERISTICS;
      } else {

        delete almdata.ORDER_CFGS_VALUE;
      }

      delete almdata.SD_ORDER_HEADER;
      delete almdata.SD_ORDER_ITEM;
      delete almdata.SD_CFGS_CUREFS;
      delete almdata.SD_CFGS_CUCFGS;
      delete almdata.SD_CFGS_CUINS;
      delete almdata.ALM_HEADER;
      resolve(almdata);
    });
  }

  Prepare_Bapi_Post(PayloadExport) {
    return new Promise((resolve, reject) => {
      if (PayloadExport.hasOwnProperty("IT_METHODS")) {
        PayloadExport.IT_METHODS.push({
          REFNUMBER: "1",
          METHOD: "SAVE",
        });
      }
      //Service Order Update RFC
      this.call("ZSD_ALM_ORDER_MAINTAIN", {
        SDALMDATA: JSON.stringify(PayloadExport),
      })
        .then((refalm) => resolve(JSON.parse(refalm.ALMRETURN)))
        .catch((error) => reject(error));
    });
  }

  //Maintenence Order Query Parameter validations
  get_alm_order_validation(reqQuery) {
    return new Promise((resolve, reject) => {
      const result = SchemaJoi.CostMaintenanceOrderQuery.validate(reqQuery);
      if (result.hasOwnProperty("error")) {
        reject(result.error.details[0].message);
      } else {
        resolve(result?.value);
      }
    });
  }

  get_cost_maintenance_order(reqOrder) {
    return new Promise(async (resolve, reject) => {
      try {
        let TEXT = '';
        if (reqOrder?.ServiceOrderID) TEXT += `AUFNR = '${reqOrder.ServiceOrderID}'`;
        if (Object.keys(reqOrder).length > 1) TEXT += ' AND ';
        if (reqOrder?.SalesOrderID) TEXT += `KDAUF = '${reqOrder.SalesOrderID}'`;

        await this.open();
        const resAUFK = await this.call("RFC_READ_TABLE", {
          QUERY_TABLE: "AUFK",
          DELIMITER: "|",
          ROWCOUNT: 9999,
          OPTIONS: [{ TEXT }],
          FIELDS: [
            {
              "FIELDNAME": "AUFNR",
              "OFFSET": "000000",
              "LENGTH": "000012",
              "TYPE": "C",
              "FIELDTEXT": "Order Number"
            },
            {
              "FIELDNAME": "KTEXT",
              "OFFSET": "000013",
              "LENGTH": "000040",
              "TYPE": "C",
              "FIELDTEXT": "Description"
            },
            {
              "FIELDNAME": "OBJNR",
              "OFFSET": "000054",
              "LENGTH": "000022",
              "TYPE": "C",
              "FIELDTEXT": "Description"
            },
            {
              "FIELDNAME": "KDAUF",
              "OFFSET": "000077",
              "LENGTH": "000010",
              "TYPE": "C",
              "FIELDTEXT": "Description"
            },
            {
              "FIELDNAME": "KDPOS",
              "OFFSET": "000088",
              "LENGTH": "000006",
              "TYPE": "C",
              "FIELDTEXT": "Description"
            }
          ]
        });

        if (resAUFK?.DATA.length < 1) reject(new Error(`No data found!`));

        //Prepare Service Order Query List for PMCO
        const OPTIONS = [];
        const aufk_data = [];
        // let text = 'OBJNR IN ( ';
        OPTIONS.push({ TEXT: 'OBJNR IN ( ' });
        resAUFK?.DATA.forEach((alm, i) => {
          const objnr = alm?.WA.slice(54, 76).trim();
          const comma = (i + 1) === resAUFK?.DATA.length ? '' : ',';
          OPTIONS.push({ TEXT: `'${objnr}'${comma}` });
          aufk_data.push({
            Sales_Order_Number: alm?.WA.slice(77, 87),
            Sales_Order_Item: alm?.WA.slice(88, 94),
            Service_Order_Number: alm?.WA.slice(0, 12),
            Service_Order_Description: alm?.WA.slice(13, 53).trim(),
            Object_Number: alm?.WA.slice(54, 76).trim()
          })
        });
        OPTIONS.push({ TEXT: `) AND WRTTP = '04'` });

        const resPMCO = await this.call("RFC_READ_TABLE", {
          QUERY_TABLE: "PMCO",
          DELIMITER: "|",
          ROWCOUNT: 9999,
          OPTIONS
        })
        await this.close();

        if (resPMCO?.DATA.length < 1) reject(new Error(`No data found!`));

        // Prepare the final data
        const CostActualPeriods = {};
        resPMCO?.DATA.forEach(row => {
          const keyParm = `${row.WA.slice(4, 32).trim()}`;
          if (!CostActualPeriods[keyParm]) {
            CostActualPeriods[keyParm] = {
              Zero: 0.00, October: 0.00, November: 0.00, December: 0.00, January: 0.00, February: 0.00, March: 0.00,
              April: 0.00, May: 0.00, June: 0.00, July: 0.00, August: 0.00, September: 0.00, Special_Period_1: 0.00,
              Special_Period_2: 0.00, Special_Period_3: 0.00, Special_Period_4: 0.00, Total: 0.00
            };
          }

          CostActualPeriods[keyParm].Zero += convertnumber(row.WA.slice(77, 92).trim());
          CostActualPeriods[keyParm].October += convertnumber(row.WA.slice(93, 108).trim());
          CostActualPeriods[keyParm].November += convertnumber(row.WA.slice(109, 124).trim());
          CostActualPeriods[keyParm].December += convertnumber(row.WA.slice(125, 140).trim());
          CostActualPeriods[keyParm].January += convertnumber(row.WA.slice(141, 156).trim());
          CostActualPeriods[keyParm].February += convertnumber(row.WA.slice(157, 172).trim());
          CostActualPeriods[keyParm].March += convertnumber(row.WA.slice(173, 188).trim());
          CostActualPeriods[keyParm].April += convertnumber(row.WA.slice(189, 204).trim());
          CostActualPeriods[keyParm].May += convertnumber(row.WA.slice(205, 220).trim());
          CostActualPeriods[keyParm].June += convertnumber(row.WA.slice(221, 236).trim());
          CostActualPeriods[keyParm].July += convertnumber(row.WA.slice(237, 252).trim());
          CostActualPeriods[keyParm].August += convertnumber(row.WA.slice(253, 268).trim());
          CostActualPeriods[keyParm].September += convertnumber(row.WA.slice(269, 284).trim());
          CostActualPeriods[keyParm].Special_Period_1 += convertnumber(row.WA.slice(285, 300).trim());
          CostActualPeriods[keyParm].Special_Period_2 += convertnumber(row.WA.slice(301, 316).trim());
          CostActualPeriods[keyParm].Special_Period_3 += convertnumber(row.WA.slice(317, 332).trim());
          CostActualPeriods[keyParm].Special_Period_4 += convertnumber(row.WA.slice(333, 348).trim());
          CostActualPeriods[keyParm].Total = CostActualPeriods[keyParm].Zero
            + CostActualPeriods[keyParm].October
            + CostActualPeriods[keyParm].November
            + CostActualPeriods[keyParm].December
            + CostActualPeriods[keyParm].January
            + CostActualPeriods[keyParm].February
            + CostActualPeriods[keyParm].March
            + CostActualPeriods[keyParm].April
            + CostActualPeriods[keyParm].May
            + CostActualPeriods[keyParm].June
            + CostActualPeriods[keyParm].July
            + CostActualPeriods[keyParm].August
            + CostActualPeriods[keyParm].September
            + CostActualPeriods[keyParm].Special_Period_1
            + CostActualPeriods[keyParm].Special_Period_2
            + CostActualPeriods[keyParm].Special_Period_3
            + CostActualPeriods[keyParm].Special_Period_4;
        });

        // Prerpare Final Array
        const final_Array = [];
        Object.keys(CostActualPeriods).forEach(key => {
          const Currency = key.split('|')[1];
          const finalAufk = aufk_data.find(aufk => aufk?.Object_Number === key.slice(0, 14));
          final_Array.push({ ...finalAufk, Currency, ...CostActualPeriods[key] });
        })

        // If no data found
        if (final_Array.length < 1) reject(new Error(`No data found!`));

        resolve(final_Array);
      } catch (error) {
        reject(error)
      }
    })
  }

  Close(data) {
    return new Promise((resolve) => {
      //Close SAP-Rfc Connection
      this.close();
      resolve(data);
    });
  }
};
